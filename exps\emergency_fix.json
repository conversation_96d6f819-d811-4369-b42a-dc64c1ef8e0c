{"prefix": "emergency_fix", "dataset": "cub", "memory_size": 0, "shuffle": true, "init_cls": 100, "increment": 10, "model_name": "ranpac", "backbone_type": "pretrained_vit_b16_224_adapter", "device": ["0"], "seed": [1993], "resume": false, "shot": 5, "use_simplecil": false, "tuned_epoch": 40, "init_lr": 0.01, "batch_size": 48, "weight_decay": 0.0005, "min_lr": 0, "ffn_num": 64, "optimizer": "sgd", "use_RP": true, "M": 10000, "fecam": false, "calibration": true, "knn_k": 5, "knn_distance_metric": "cosine", "knn_weight_decay": 0.1, "knn_adaptive_k": true, "knn_temperature": 16.0, "k_min": 3, "k_max": 21, "dynamic_k_method": "cosine_similarity", "cosine_temperature": 16.0, "_comment_emergency": "=== EMERGENCY FIX: 完全禁用解耦适配器 ===", "use_disentangled_adapter": false, "_comment_note": "此配置文件用于紧急修复性能下降问题，完全禁用解耦适配器功能"}