"""
NaN修复测试脚本
用于验证解耦损失的数值稳定性修复
"""

import torch
import numpy as np
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)

def test_disentanglement_loss_stability():
    """测试解耦损失的数值稳定性"""
    print("=" * 50)
    print("测试解耦损失数值稳定性")
    print("=" * 50)
    
    try:
        from utils.disentanglement_losses import DisentanglementLossManager
        
        # 创建损失管理器
        loss_manager = DisentanglementLossManager(
            decorrelation_weight=0.1,
            orthogonal_weight=0.01,
            mutual_info_weight=0.01,
            contrastive_weight=0.01,
            temperature=0.1
        )
        
        print("✓ 损失管理器创建成功")
        
        # 测试正常情况
        batch_size = 8
        identity_dim = 32
        variation_dim = 32
        
        identity_features = torch.randn(batch_size, identity_dim)
        variation_features = torch.randn(batch_size, variation_dim)
        labels = torch.randint(0, 4, (batch_size,))
        
        total_loss, loss_dict = loss_manager(identity_features, variation_features, labels)
        
        print(f"✓ 正常情况测试通过:")
        print(f"  总损失: {total_loss.item():.6f}")
        for loss_name, loss_value in loss_dict.items():
            print(f"  {loss_name}: {loss_value:.6f}")
        
        # 测试边界情况
        print("\n测试边界情况:")
        
        # 1. 小批量
        small_identity = torch.randn(1, identity_dim)
        small_variation = torch.randn(1, variation_dim)
        small_loss, small_dict = loss_manager(small_identity, small_variation)
        print(f"✓ 小批量测试: 损失={small_loss.item():.6f}")
        
        # 2. 零特征
        zero_identity = torch.zeros(batch_size, identity_dim)
        zero_variation = torch.zeros(batch_size, variation_dim)
        zero_loss, zero_dict = loss_manager(zero_identity, zero_variation)
        print(f"✓ 零特征测试: 损失={zero_loss.item():.6f}")
        
        # 3. 极大特征
        large_identity = torch.randn(batch_size, identity_dim) * 1000
        large_variation = torch.randn(batch_size, variation_dim) * 1000
        large_loss, large_dict = loss_manager(large_identity, large_variation)
        print(f"✓ 极大特征测试: 损失={large_loss.item():.6f}")
        
        # 4. NaN输入
        nan_identity = torch.full((batch_size, identity_dim), float('nan'))
        nan_variation = torch.randn(batch_size, variation_dim)
        nan_loss, nan_dict = loss_manager(nan_identity, nan_variation)
        print(f"✓ NaN输入测试: 损失={nan_loss.item():.6f}")
        
        # 检查所有损失是否为有限值
        all_finite = True
        test_cases = [
            ("正常", total_loss),
            ("小批量", small_loss),
            ("零特征", zero_loss),
            ("极大特征", large_loss),
            ("NaN输入", nan_loss)
        ]
        
        for case_name, loss in test_cases:
            if torch.isnan(loss) or torch.isinf(loss):
                print(f"✗ {case_name}情况产生了NaN/Inf损失: {loss.item()}")
                all_finite = False
            else:
                print(f"✓ {case_name}情况损失正常: {loss.item():.6f}")
        
        return all_finite
        
    except Exception as e:
        print(f"✗ 解耦损失测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_disentangled_adapter_stability():
    """测试解耦适配器的数值稳定性"""
    print("\n" + "=" * 50)
    print("测试解耦适配器数值稳定性")
    print("=" * 50)
    
    try:
        from backbone.vision_transformer_adapter import DisentangledAdapter
        
        # 创建简单配置
        class SimpleConfig:
            def __init__(self):
                self.d_model = 768
                self.attn_bn = 64
        
        config = SimpleConfig()
        
        # 创建适配器
        adapter = DisentangledAdapter(
            config=config,
            identity_bottleneck=32,
            variation_bottleneck=32,
            dropout=0.1,
            disentangle_loss_weight=0.0001,  # 极小的权重
            orthogonal_constraint=False  # 禁用正交约束
        )
        
        print("✓ 解耦适配器创建成功")
        
        # 测试前向传播
        batch_size, seq_len, d_model = 4, 197, 768
        x = torch.randn(batch_size, seq_len, d_model)
        
        # 解耦前向传播
        output, identity_features, variation_features = adapter(x, return_separate=True)
        print(f"✓ 前向传播成功: identity {identity_features.shape}, variation {variation_features.shape}")
        
        # 测试损失计算
        disentangle_loss = adapter.compute_disentanglement_loss(identity_features, variation_features)
        print(f"✓ 损失计算成功: {disentangle_loss.item():.8f}")
        
        # 测试梯度计算
        total_loss = output.sum() + disentangle_loss
        total_loss.backward()
        print("✓ 梯度计算成功")
        
        # 检查损失是否为有限值
        if torch.isnan(disentangle_loss) or torch.isinf(disentangle_loss):
            print(f"✗ 解耦适配器产生了NaN/Inf损失: {disentangle_loss.item()}")
            return False
        else:
            print(f"✓ 解耦适配器损失正常: {disentangle_loss.item():.8f}")
            return True
        
    except Exception as e:
        print(f"✗ 解耦适配器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_training_simulation():
    """模拟训练过程测试"""
    print("\n" + "=" * 50)
    print("模拟训练过程测试")
    print("=" * 50)
    
    try:
        from utils.disentanglement_losses import DisentanglementLossManager
        import torch.nn.functional as F
        
        # 创建损失管理器
        loss_manager = DisentanglementLossManager(
            decorrelation_weight=0.1,
            orthogonal_weight=0.01,
            mutual_info_weight=0.01,
            contrastive_weight=0.01
        )
        
        # 模拟训练循环
        print("模拟5个训练步骤:")
        
        for step in range(5):
            # 模拟批次数据
            batch_size = 16
            identity_features = torch.randn(batch_size, 32, requires_grad=True)
            variation_features = torch.randn(batch_size, 32, requires_grad=True)
            labels = torch.randint(0, 10, (batch_size,))
            
            # 模拟分类损失
            logits = torch.randn(batch_size, 10, requires_grad=True)
            classification_loss = F.cross_entropy(logits, labels)
            
            # 计算解耦损失
            disentangle_loss, loss_dict = loss_manager(identity_features, variation_features, labels)
            
            # 总损失
            total_loss = classification_loss + 0.0001 * disentangle_loss
            
            # 检查损失
            if torch.isnan(total_loss) or torch.isinf(total_loss):
                print(f"✗ 步骤 {step}: NaN/Inf损失 - 分类: {classification_loss.item():.4f}, 解耦: {disentangle_loss.item():.6f}")
                return False
            else:
                print(f"✓ 步骤 {step}: 正常损失 - 分类: {classification_loss.item():.4f}, 解耦: {disentangle_loss.item():.6f}, 总计: {total_loss.item():.4f}")
            
            # 反向传播
            total_loss.backward()
            
            # 检查梯度
            if torch.isnan(identity_features.grad).any() or torch.isnan(variation_features.grad).any():
                print(f"✗ 步骤 {step}: NaN梯度")
                return False
        
        print("✓ 所有训练步骤都成功完成")
        return True
        
    except Exception as e:
        print(f"✗ 训练模拟测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🔧 NaN修复验证测试")
    print("=" * 50)
    
    tests = [
        ("解耦损失稳定性", test_disentanglement_loss_stability),
        ("解耦适配器稳定性", test_disentangled_adapter_stability),
        ("训练过程模拟", test_training_simulation),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 50)
    print("测试结果总结")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("\n🎉 所有数值稳定性测试都通过！")
        print("\n📝 下一步:")
        print("1. 运行: python main.py --config exps/debug_safe.json")
        print("2. 检查是否还有NaN损失")
        print("3. 如果成功，逐步增加损失权重")
        return True
    else:
        print(f"\n⚠ {total - passed} 项测试失败，需要进一步修复。")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
