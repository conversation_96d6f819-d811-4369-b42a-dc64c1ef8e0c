"""
解耦损失函数模块
实现各种解耦约束机制，确保身份向量和变化向量之间的互信息最小化
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np


class DisentanglementLossManager(nn.Module):
    """
    解耦损失管理器，集成多种解耦约束机制
    """
    def __init__(self, 
                 decorrelation_weight=1.0,
                 orthogonal_weight=0.1,
                 mutual_info_weight=0.5,
                 contrastive_weight=0.3,
                 temperature=0.1):
        super().__init__()
        
        self.decorrelation_weight = decorrelation_weight
        self.orthogonal_weight = orthogonal_weight
        self.mutual_info_weight = mutual_info_weight
        self.contrastive_weight = contrastive_weight
        self.temperature = temperature
        
    def compute_decorrelation_loss(self, identity_features, variation_features):
        """
        计算去相关损失，最小化身份和变化特征之间的相关性
        
        Args:
            identity_features: [batch_size, identity_dim] 身份特征
            variation_features: [batch_size, variation_dim] 变化特征
            
        Returns:
            decorrelation_loss: 去相关损失
        """
        # 中心化特征
        identity_centered = identity_features - identity_features.mean(dim=0, keepdim=True)
        variation_centered = variation_features - variation_features.mean(dim=0, keepdim=True)
        
        # 归一化
        identity_norm = F.normalize(identity_centered, p=2, dim=0)
        variation_norm = F.normalize(variation_centered, p=2, dim=0)
        
        # 计算互相关矩阵
        cross_correlation = torch.mm(identity_norm.T, variation_norm)
        
        # Frobenius范数作为去相关损失
        decorrelation_loss = torch.norm(cross_correlation, p='fro') ** 2
        
        return decorrelation_loss * self.decorrelation_weight
    
    def compute_orthogonal_loss(self, features):
        """
        计算正交损失，促进特征内部的正交性
        
        Args:
            features: [batch_size, feature_dim] 特征矩阵
            
        Returns:
            orthogonal_loss: 正交损失
        """
        # 中心化和归一化
        features_centered = features - features.mean(dim=0, keepdim=True)
        features_norm = F.normalize(features_centered, p=2, dim=0)
        
        # 计算Gram矩阵
        gram_matrix = torch.mm(features_norm.T, features_norm)
        
        # 正交损失：Gram矩阵与单位矩阵的差异
        identity_matrix = torch.eye(gram_matrix.size(0), device=gram_matrix.device)
        orthogonal_loss = torch.norm(gram_matrix - identity_matrix, p='fro') ** 2
        
        return orthogonal_loss * self.orthogonal_weight
    
    def compute_mutual_information_loss(self, identity_features, variation_features):
        """
        计算互信息损失的近似（基于MINE或其他方法）
        这里使用简化的基于相关性的近似
        
        Args:
            identity_features: [batch_size, identity_dim] 身份特征
            variation_features: [batch_size, variation_dim] 变化特征
            
        Returns:
            mi_loss: 互信息损失
        """
        # 简化的互信息近似：基于特征的方差和协方差
        batch_size = identity_features.size(0)
        
        # 计算联合分布的"熵"（基于协方差矩阵的行列式）
        combined_features = torch.cat([identity_features, variation_features], dim=1)
        combined_cov = torch.cov(combined_features.T)
        
        # 计算边际分布的"熵"
        identity_cov = torch.cov(identity_features.T)
        variation_cov = torch.cov(variation_features.T)
        
        # 使用行列式的对数作为熵的近似
        joint_entropy = torch.logdet(combined_cov + 1e-6 * torch.eye(combined_cov.size(0), device=combined_cov.device))
        marginal_entropy = (torch.logdet(identity_cov + 1e-6 * torch.eye(identity_cov.size(0), device=identity_cov.device)) + 
                           torch.logdet(variation_cov + 1e-6 * torch.eye(variation_cov.size(0), device=variation_cov.device)))
        
        # 互信息 = 边际熵 - 联合熵
        mutual_info = marginal_entropy - joint_entropy
        
        # 我们希望最小化互信息
        mi_loss = mutual_info * self.mutual_info_weight
        
        return mi_loss
    
    def compute_contrastive_loss(self, identity_features, variation_features, labels=None):
        """
        计算对比损失，确保同类样本的身份特征相似，变化特征不同
        
        Args:
            identity_features: [batch_size, identity_dim] 身份特征
            variation_features: [batch_size, variation_dim] 变化特征
            labels: [batch_size] 类别标签（可选）
            
        Returns:
            contrastive_loss: 对比损失
        """
        if labels is None:
            # 如果没有标签，使用自监督对比学习
            return self._compute_self_supervised_contrastive_loss(identity_features, variation_features)
        else:
            # 有监督对比学习
            return self._compute_supervised_contrastive_loss(identity_features, variation_features, labels)
    
    def _compute_self_supervised_contrastive_loss(self, identity_features, variation_features):
        """
        自监督对比损失：身份特征应该对变化不敏感
        """
        batch_size = identity_features.size(0)
        
        # 计算身份特征的相似性矩阵
        identity_sim = F.cosine_similarity(identity_features.unsqueeze(1), identity_features.unsqueeze(0), dim=2)
        
        # 计算变化特征的相似性矩阵
        variation_sim = F.cosine_similarity(variation_features.unsqueeze(1), variation_features.unsqueeze(0), dim=2)
        
        # 对比损失：身份相似但变化不同的样本对应该有高身份相似度和低变化相似度
        # 这里使用简化版本：最小化身份和变化相似性的相关性
        identity_sim_flat = identity_sim.view(-1)
        variation_sim_flat = variation_sim.view(-1)
        
        # 计算相关系数
        correlation = F.cosine_similarity(identity_sim_flat.unsqueeze(0), variation_sim_flat.unsqueeze(0), dim=1)
        
        contrastive_loss = correlation.abs() * self.contrastive_weight
        
        return contrastive_loss
    
    def _compute_supervised_contrastive_loss(self, identity_features, variation_features, labels):
        """
        有监督对比损失
        """
        batch_size = identity_features.size(0)
        
        # 创建正负样本掩码
        labels = labels.view(-1, 1)
        mask = torch.eq(labels, labels.T).float()
        
        # 计算身份特征相似性
        identity_sim = F.cosine_similarity(identity_features.unsqueeze(1), identity_features.unsqueeze(0), dim=2)
        identity_sim = identity_sim / self.temperature
        
        # 计算变化特征相似性
        variation_sim = F.cosine_similarity(variation_features.unsqueeze(1), variation_features.unsqueeze(0), dim=2)
        variation_sim = variation_sim / self.temperature
        
        # 对于同类样本：身份特征应该相似，变化特征应该不同
        positive_mask = mask * (1 - torch.eye(batch_size, device=mask.device))
        
        # 身份特征对比损失：同类样本应该相似
        identity_positive = (identity_sim * positive_mask).sum(dim=1) / (positive_mask.sum(dim=1) + 1e-8)
        identity_negative = (identity_sim * (1 - mask)).sum(dim=1) / ((1 - mask).sum(dim=1) + 1e-8)
        identity_contrastive = -torch.log(torch.exp(identity_positive) / (torch.exp(identity_positive) + torch.exp(identity_negative)) + 1e-8).mean()
        
        # 变化特征对比损失：同类样本的变化特征应该不同
        variation_positive = (variation_sim * positive_mask).sum(dim=1) / (positive_mask.sum(dim=1) + 1e-8)
        variation_contrastive = variation_positive.mean()  # 最小化同类样本的变化相似性
        
        contrastive_loss = (identity_contrastive + variation_contrastive) * self.contrastive_weight
        
        return contrastive_loss
    
    def forward(self, identity_features, variation_features, labels=None):
        """
        计算总的解耦损失
        
        Args:
            identity_features: [batch_size, identity_dim] 身份特征
            variation_features: [batch_size, variation_dim] 变化特征
            labels: [batch_size] 类别标签（可选）
            
        Returns:
            total_loss: 总解耦损失
            loss_dict: 各项损失的详细信息
        """
        loss_dict = {}
        
        # 去相关损失
        decorrelation_loss = self.compute_decorrelation_loss(identity_features, variation_features)
        loss_dict['decorrelation'] = decorrelation_loss.item()
        
        # 正交损失
        identity_orthogonal_loss = self.compute_orthogonal_loss(identity_features)
        variation_orthogonal_loss = self.compute_orthogonal_loss(variation_features)
        orthogonal_loss = identity_orthogonal_loss + variation_orthogonal_loss
        loss_dict['orthogonal'] = orthogonal_loss.item()
        
        # 互信息损失
        if identity_features.size(0) > 1:  # 需要至少2个样本来计算协方差
            mi_loss = self.compute_mutual_information_loss(identity_features, variation_features)
            loss_dict['mutual_info'] = mi_loss.item()
        else:
            mi_loss = torch.tensor(0.0, device=identity_features.device)
            loss_dict['mutual_info'] = 0.0
        
        # 对比损失
        if identity_features.size(0) > 1:
            contrastive_loss = self.compute_contrastive_loss(identity_features, variation_features, labels)
            loss_dict['contrastive'] = contrastive_loss.item()
        else:
            contrastive_loss = torch.tensor(0.0, device=identity_features.device)
            loss_dict['contrastive'] = 0.0
        
        # 总损失
        total_loss = decorrelation_loss + orthogonal_loss + mi_loss + contrastive_loss
        loss_dict['total'] = total_loss.item()
        
        return total_loss, loss_dict


class AdaptiveDisentanglementLoss(DisentanglementLossManager):
    """
    自适应解耦损失，根据训练进度调整各项损失的权重
    """
    def __init__(self, 
                 decorrelation_weight=1.0,
                 orthogonal_weight=0.1,
                 mutual_info_weight=0.5,
                 contrastive_weight=0.3,
                 temperature=0.1,
                 warmup_epochs=10,
                 total_epochs=100):
        super().__init__(decorrelation_weight, orthogonal_weight, mutual_info_weight, contrastive_weight, temperature)
        
        self.warmup_epochs = warmup_epochs
        self.total_epochs = total_epochs
        self.current_epoch = 0
        
        # 保存初始权重
        self.initial_decorrelation_weight = decorrelation_weight
        self.initial_orthogonal_weight = orthogonal_weight
        self.initial_mutual_info_weight = mutual_info_weight
        self.initial_contrastive_weight = contrastive_weight
    
    def update_epoch(self, epoch):
        """更新当前epoch，调整损失权重"""
        self.current_epoch = epoch
        
        # 计算权重调整因子
        if epoch < self.warmup_epochs:
            # 预热阶段：逐渐增加解耦损失权重
            factor = epoch / self.warmup_epochs
        else:
            # 正常训练阶段：保持权重或逐渐减少
            factor = 1.0 - 0.5 * (epoch - self.warmup_epochs) / (self.total_epochs - self.warmup_epochs)
            factor = max(factor, 0.1)  # 最小保持10%的权重
        
        # 更新权重
        self.decorrelation_weight = self.initial_decorrelation_weight * factor
        self.orthogonal_weight = self.initial_orthogonal_weight * factor
        self.mutual_info_weight = self.initial_mutual_info_weight * factor
        self.contrastive_weight = self.initial_contrastive_weight * factor
