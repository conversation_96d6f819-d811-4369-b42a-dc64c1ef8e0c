# --------------------------------------------------------
# References:
# https://github.com/jxhe/unify-parameter-efficient-tuning
# --------------------------------------------------------

import math
import torch
import torch.nn as nn
from timm.models.layers import DropPath
# --------------------------------------------------------
# References:
# timm: https://github.com/rwightman/pytorch-image-models/tree/master/timm
# DeiT: https://github.com/facebookresearch/deit
# MAE: https://github.com/facebookresearch/mae
# --------------------------------------------------------
import timm
from functools import partial
from collections import OrderedDict
import torch
import torch.nn as nn
from timm.models.vision_transformer import PatchEmbed
from timm.models.registry import register_model

import logging
import os
from collections import OrderedDict
import torch



class Adapter(nn.Module):
    def __init__(self,
                 config=None,
                 d_model=None,
                 bottleneck=None,
                 dropout=0.0,
                 init_option="bert",
                 adapter_scalar="1.0",
                 adapter_layernorm_option="in"):
        super().__init__()
        self.n_embd = config.d_model if d_model is None else d_model
        self.down_size = config.attn_bn if bottleneck is None else bottleneck

        #_before
        self.adapter_layernorm_option = adapter_layernorm_option

        self.adapter_layer_norm_before = None
        if adapter_layernorm_option == "in" or adapter_layernorm_option == "out":
            self.adapter_layer_norm_before = nn.LayerNorm(self.n_embd)

        if adapter_scalar == "learnable_scalar":
            self.scale = nn.Parameter(torch.ones(1))
        else:
            self.scale = float(adapter_scalar)

        self.down_proj = nn.Linear(self.n_embd, self.down_size)
        self.non_linear_func = nn.ReLU()
        self.up_proj = nn.Linear(self.down_size, self.n_embd)

        self.dropout = dropout
        if init_option == "bert":
            raise NotImplementedError
        elif init_option == "lora":
            with torch.no_grad():
                nn.init.kaiming_uniform_(self.down_proj.weight, a=math.sqrt(5))
                nn.init.zeros_(self.up_proj.weight)
                nn.init.zeros_(self.down_proj.bias)
                nn.init.zeros_(self.up_proj.bias)

    def forward(self, x, add_residual=True, residual=None):
        residual = x if residual is None else residual
        if self.adapter_layernorm_option == 'in':
            x = self.adapter_layer_norm_before(x)

        down = self.down_proj(x)
        down = self.non_linear_func(down)
        down = nn.functional.dropout(down, p=self.dropout, training=self.training)
        up = self.up_proj(down)

        up = up * self.scale

        if self.adapter_layernorm_option == 'out':
            up = self.adapter_layer_norm_before(up)

        if add_residual:
            output = up + residual
        else:
            output = up

        return output


class DisentangledAdapter(nn.Module):
    """
    解耦泛化适配器：双分支架构用于分离类别身份和类内变化

    Architecture:
    - Identity Branch: 捕获稳定的类别语义特征，用于分类
    - Variation Branch: 捕获类内变化信息，用于协方差建模
    """
    def __init__(self,
                 config=None,
                 d_model=None,
                 identity_bottleneck=None,
                 variation_bottleneck=None,
                 dropout=0.0,
                 init_option="lora",
                 adapter_scalar="1.0",
                 adapter_layernorm_option="in",
                 disentangle_loss_weight=0.1,
                 orthogonal_constraint=True):
        super().__init__()

        self.n_embd = config.d_model if d_model is None else d_model
        self.identity_bottleneck = identity_bottleneck or (config.attn_bn if config else 64)
        self.variation_bottleneck = variation_bottleneck or (config.attn_bn if config else 64)

        self.disentangle_loss_weight = disentangle_loss_weight
        self.orthogonal_constraint = orthogonal_constraint

        # 共享的输入层归一化
        self.adapter_layernorm_option = adapter_layernorm_option
        self.input_layer_norm = None
        if adapter_layernorm_option == "in" or adapter_layernorm_option == "out":
            self.input_layer_norm = nn.LayerNorm(self.n_embd)

        # 身份分支 - 用于类别识别
        self.identity_branch = self._build_branch(
            self.n_embd, self.identity_bottleneck, "identity", init_option
        )

        # 变化分支 - 用于捕获类内变化
        self.variation_branch = self._build_branch(
            self.n_embd, self.variation_bottleneck, "variation", init_option
        )

        # 输出投影层
        self.identity_output_dim = self.n_embd // 2
        self.variation_output_dim = self.n_embd // 2

        self.identity_output_proj = nn.Linear(self.identity_bottleneck, self.identity_output_dim)
        self.variation_output_proj = nn.Linear(self.variation_bottleneck, self.variation_output_dim)

        # 缩放参数
        if adapter_scalar == "learnable_scalar":
            self.identity_scale = nn.Parameter(torch.ones(1))
            self.variation_scale = nn.Parameter(torch.ones(1))
        else:
            self.identity_scale = float(adapter_scalar)
            self.variation_scale = float(adapter_scalar)

        self.dropout = dropout

        # 初始化输出投影层
        if init_option == "lora":
            with torch.no_grad():
                nn.init.kaiming_uniform_(self.identity_output_proj.weight, a=math.sqrt(5))
                nn.init.zeros_(self.identity_output_proj.bias)
                nn.init.kaiming_uniform_(self.variation_output_proj.weight, a=math.sqrt(5))
                nn.init.zeros_(self.variation_output_proj.bias)

    def _build_branch(self, input_dim, bottleneck_dim, branch_type, init_option):
        """构建单个分支"""
        branch = nn.Sequential(
            nn.Linear(input_dim, bottleneck_dim),
            nn.ReLU(),
            nn.Dropout(self.dropout)
        )

        # 分支特定的初始化
        if init_option == "lora":
            with torch.no_grad():
                if branch_type == "identity":
                    # 身份分支：更保守的初始化，保持稳定性
                    nn.init.kaiming_uniform_(branch[0].weight, a=math.sqrt(5))
                    nn.init.zeros_(branch[0].bias)
                else:
                    # 变化分支：更激进的初始化，捕获变化
                    nn.init.xavier_uniform_(branch[0].weight)
                    nn.init.zeros_(branch[0].bias)

        return branch

    def forward(self, x, add_residual=True, residual=None, return_separate=False):
        """
        前向传播

        Args:
            x: 输入特征 [batch_size, seq_len, d_model]
            add_residual: 是否添加残差连接
            residual: 残差项
            return_separate: 是否返回分离的身份和变化特征

        Returns:
            如果return_separate=True: (combined_output, identity_features, variation_features)
            否则: combined_output
        """
        batch_size, seq_len, _ = x.shape
        residual = x if residual is None else residual

        # 输入归一化
        if self.adapter_layernorm_option == 'in' and self.input_layer_norm is not None:
            x_norm = self.input_layer_norm(x)
        else:
            x_norm = x

        # 双分支处理
        identity_features = self.identity_branch(x_norm)  # [batch_size, seq_len, identity_bottleneck]
        variation_features = self.variation_branch(x_norm)  # [batch_size, seq_len, variation_bottleneck]

        # 输出投影
        identity_output = self.identity_output_proj(identity_features)  # [batch_size, seq_len, identity_output_dim]
        variation_output = self.variation_output_proj(variation_features)  # [batch_size, seq_len, variation_output_dim]

        # 应用缩放
        identity_output = identity_output * self.identity_scale
        variation_output = variation_output * self.variation_scale

        # 拼接特征
        combined_features = torch.cat([identity_output, variation_output], dim=-1)  # [batch_size, seq_len, n_embd]

        # 输出归一化
        if self.adapter_layernorm_option == 'out' and self.input_layer_norm is not None:
            combined_features = self.input_layer_norm(combined_features)

        # 残差连接
        if add_residual:
            output = combined_features + residual
        else:
            output = combined_features

        if return_separate:
            return output, identity_output, variation_output
        else:
            return output

    def compute_disentanglement_loss(self, identity_features, variation_features):
        """
        计算解耦损失，确保身份和变化特征的独立性

        Args:
            identity_features: 身份特征 [batch_size, seq_len, identity_output_dim]
            variation_features: 变化特征 [batch_size, seq_len, variation_output_dim]

        Returns:
            disentanglement_loss: 解耦损失
        """
        # 展平特征用于计算相关性
        batch_size, seq_len, _ = identity_features.shape
        identity_flat = identity_features.view(-1, identity_features.size(-1))  # [batch_size*seq_len, identity_dim]
        variation_flat = variation_features.view(-1, variation_features.size(-1))  # [batch_size*seq_len, variation_dim]

        # 计算互相关矩阵
        identity_centered = identity_flat - identity_flat.mean(dim=0, keepdim=True)
        variation_centered = variation_flat - variation_flat.mean(dim=0, keepdim=True)

        # 归一化
        identity_norm = torch.nn.functional.normalize(identity_centered, p=2, dim=0)
        variation_norm = torch.nn.functional.normalize(variation_centered, p=2, dim=0)

        # 计算互相关矩阵 [identity_dim, variation_dim]
        cross_correlation = torch.mm(identity_norm.T, variation_norm)

        # 解耦损失：最小化互相关矩阵的Frobenius范数
        disentanglement_loss = torch.norm(cross_correlation, p='fro') ** 2

        # 正交约束（可选）
        if self.orthogonal_constraint:
            # 身份特征内部正交性
            identity_gram = torch.mm(identity_norm.T, identity_norm)
            identity_orthogonal_loss = torch.norm(identity_gram - torch.eye(identity_gram.size(0), device=identity_gram.device), p='fro') ** 2

            # 变化特征内部正交性
            variation_gram = torch.mm(variation_norm.T, variation_norm)
            variation_orthogonal_loss = torch.norm(variation_gram - torch.eye(variation_gram.size(0), device=variation_gram.device), p='fro') ** 2

            disentanglement_loss += 0.1 * (identity_orthogonal_loss + variation_orthogonal_loss)

        return disentanglement_loss * self.disentangle_loss_weight





class Attention(nn.Module):
    def __init__(self, dim, num_heads=8, qkv_bias=False, attn_drop=0., proj_drop=0.,):
        super().__init__()
        self.num_heads = num_heads
        head_dim = dim // num_heads
        self.head_dim = dim // num_heads
        self.scale = head_dim ** -0.5

        self.q_proj = nn.Linear(dim, dim, bias=qkv_bias)
        self.v_proj = nn.Linear(dim, dim, bias=qkv_bias)
        self.k_proj = nn.Linear(dim, dim, bias=qkv_bias)

        self.attn_drop = nn.Dropout(attn_drop)
        self.proj = nn.Linear(dim, dim)
        self.proj_drop = nn.Dropout(proj_drop)

    def _shape(self, tensor: torch.Tensor, seq_len: int, bsz: int):
        return tensor.view(bsz, seq_len, self.num_heads, self.head_dim).transpose(1, 2).contiguous()

    def forward(self, x):
        B, N, C = x.shape

        q = self.q_proj(x)
        k = self._shape(self.k_proj(x), -1, B).view(B * self.num_heads, -1, self.head_dim)
        v = self._shape(self.v_proj(x), -1, B).view(B * self.num_heads, -1, self.head_dim)
        q = self._shape(q, N, B).view(B * self.num_heads, -1, self.head_dim)

        # attn = (q @ k.transpose(-2, -1)) * self.scale
        attn_weights = torch.bmm(q, k.transpose(1, 2)) * self.scale

        attn_weights = nn.functional.softmax(attn_weights, dim=-1)
        attn_probs = self.attn_drop(attn_weights)
        attn_output = torch.bmm(attn_probs, v)

        attn_output = attn_output.view(B, self.num_heads, N, self.head_dim)
        attn_output = attn_output.transpose(1, 2)
        attn_output = attn_output.reshape(B, N, C)

        x = self.proj(attn_output)
        x = self.proj_drop(x)

        return x


class Block(nn.Module):

    def __init__(self, dim, num_heads, mlp_ratio=4., qkv_bias=False, drop=0., attn_drop=0.,
                 drop_path=0., act_layer=nn.GELU, norm_layer=nn.LayerNorm, config=None, layer_id=None):
        super().__init__()
        self.config = config
        self.norm1 = norm_layer(dim)
        self.attn = Attention(dim, num_heads=num_heads, qkv_bias=qkv_bias, attn_drop=attn_drop, proj_drop=drop)
        # NOTE: drop path for stochastic depth, we shall see if this is better than dropout here
        self.drop_path = DropPath(drop_path) if drop_path > 0. else nn.Identity()
        self.norm2 = norm_layer(dim)
        mlp_hidden_dim = int(dim * mlp_ratio)

        self.fc1 = nn.Linear(dim, mlp_hidden_dim)
        self.fc2 = nn.Linear(mlp_hidden_dim, dim)
        self.act = act_layer()
        self.mlp_drop = nn.Dropout(drop)

        # 支持传统适配器和解耦适配器
        if config.ffn_adapt:
            if getattr(config, 'use_disentangled_adapter', False):
                # 使用解耦适配器
                self.adaptmlp = DisentangledAdapter(
                    self.config,
                    dropout=0.1,
                    identity_bottleneck=getattr(config, 'identity_bottleneck', config.ffn_num),
                    variation_bottleneck=getattr(config, 'variation_bottleneck', config.ffn_num),
                    init_option=config.ffn_adapter_init_option,
                    adapter_scalar=config.ffn_adapter_scalar,
                    adapter_layernorm_option=config.ffn_adapter_layernorm_option,
                    disentangle_loss_weight=getattr(config, 'disentangle_loss_weight', 0.1),
                    orthogonal_constraint=getattr(config, 'orthogonal_constraint', True)
                )
                self.use_disentangled = True
            else:
                # 使用传统适配器
                self.adaptmlp = Adapter(self.config, dropout=0.1, bottleneck=config.ffn_num,
                                        init_option=config.ffn_adapter_init_option,
                                        adapter_scalar=config.ffn_adapter_scalar,
                                        adapter_layernorm_option=config.ffn_adapter_layernorm_option,
                                        )
                self.use_disentangled = False
        else:
            self.use_disentangled = False

    def forward(self, x, return_disentangled_features=False):
        """
        前向传播，支持返回解耦特征

        Args:
            x: 输入特征
            return_disentangled_features: 是否返回解耦的身份和变化特征

        Returns:
            如果return_disentangled_features=True且使用解耦适配器:
                (output, identity_features, variation_features, disentanglement_loss)
            否则: output
        """
        x = x + self.drop_path(self.attn(self.norm1(x)))

        # 适配器处理
        adapt_x = None
        identity_features = None
        variation_features = None
        disentanglement_loss = None

        if self.config.ffn_adapt and self.config.ffn_option == 'parallel':
            if self.use_disentangled and return_disentangled_features:
                adapt_x, identity_features, variation_features = self.adaptmlp(
                    x, add_residual=False, return_separate=True
                )
                # 计算解耦损失
                disentanglement_loss = self.adaptmlp.compute_disentanglement_loss(
                    identity_features, variation_features
                )
            else:
                adapt_x = self.adaptmlp(x, add_residual=False)

        residual = x
        x = self.mlp_drop(self.act(self.fc1(self.norm2(x))))
        x = self.drop_path(self.mlp_drop(self.fc2(x)))

        if self.config.ffn_adapt:
            if self.config.ffn_option == 'sequential':
                if self.use_disentangled and return_disentangled_features:
                    x, identity_features, variation_features = self.adaptmlp(
                        x, return_separate=True
                    )
                    disentanglement_loss = self.adaptmlp.compute_disentanglement_loss(
                        identity_features, variation_features
                    )
                else:
                    x = self.adaptmlp(x)
            elif self.config.ffn_option == 'parallel':
                x = x + adapt_x
            else:
                raise ValueError(self.config.ffn_adapt)

        x = residual + x

        if return_disentangled_features and self.use_disentangled:
            return x, identity_features, variation_features, disentanglement_loss
        else:
            return x





class VisionTransformer(nn.Module):
    """ Vision Transformer with support for global average pooling
    """
    def __init__(self, global_pool=False, img_size=224, patch_size=16, in_chans=3, num_classes=1000, embed_dim=768, depth=12,
                 num_heads=12, mlp_ratio=4., qkv_bias=True, representation_size=None, distilled=False,
                 drop_rate=0., attn_drop_rate=0., drop_path_rate=0., embed_layer=PatchEmbed, norm_layer=None,
                 act_layer=None, weight_init='', tuning_config=None):
        super().__init__()


        print("I'm using ViT with adapters.")
        self.tuning_config = tuning_config
        self.num_classes = num_classes
        self.num_features = self.embed_dim = embed_dim  # num_features for consistency with other models
        self.num_tokens = 2 if distilled else 1
        norm_layer = norm_layer or partial(nn.LayerNorm, eps=1e-6)
        act_layer = act_layer or nn.GELU

        self.patch_embed = embed_layer(
            img_size=img_size, patch_size=patch_size, in_chans=in_chans, embed_dim=embed_dim)
        num_patches = self.patch_embed.num_patches

        self.cls_token = nn.Parameter(torch.zeros(1, 1, embed_dim))
        self.dist_token = nn.Parameter(torch.zeros(1, 1, embed_dim)) if distilled else None
        self.pos_embed = nn.Parameter(torch.zeros(1, num_patches + self.num_tokens, embed_dim))
        self.pos_drop = nn.Dropout(p=drop_rate)

        dpr = [x.item() for x in torch.linspace(0, drop_path_rate, depth)]  # stochastic depth decay rule
        self.blocks = nn.Sequential(*[
            Block(
                dim=embed_dim, num_heads=num_heads, mlp_ratio=mlp_ratio, qkv_bias=qkv_bias, drop=drop_rate,
                attn_drop=attn_drop_rate, drop_path=dpr[i], norm_layer=norm_layer, act_layer=act_layer,
                config=tuning_config, layer_id=i,
            )
            for i in range(depth)])
        self.norm = norm_layer(embed_dim)

        # Representation layer
        if representation_size and not distilled:
            self.num_features = representation_size
            self.pre_logits = nn.Sequential(OrderedDict([
                ('fc', nn.Linear(embed_dim, representation_size)),
                ('act', nn.Tanh())
            ]))
        else:
            self.pre_logits = nn.Identity()

        # Classifier head(s)
        self.head = nn.Linear(self.num_features, num_classes) if num_classes > 0 else nn.Identity()
        self.head_dist = None
        if distilled:
            self.head_dist = nn.Linear(self.embed_dim, self.num_classes) if num_classes > 0 else nn.Identity()

        # self.init_weights(weight_init)

        ######### MAE begins ############
        self.global_pool = global_pool
        if self.global_pool:
            self.fc_norm = norm_layer(embed_dim)

            del self.norm  # remove the original norm

        ######## Adapter begins #########
        if tuning_config.vpt_on:
            assert tuning_config.vpt_num > 0, tuning_config.vpt_num
            # properly registered
            self.embeddings = nn.ParameterList(  # batch, num_prompt, embed_dim
                [nn.Parameter(torch.empty(1, self.tuning_config.vpt_num, embed_dim)) for _ in
                 range(depth)])
            for eee in self.embeddings:
                torch.nn.init.xavier_uniform_(eee.data)

    def init_weights(self, mode=''):
        raise NotImplementedError()

    @torch.jit.ignore
    def no_weight_decay(self):
        return {'pos_embed', 'cls_token', 'dist_token'}

    def get_classifier(self):
        if self.dist_token is None:
            return self.head
        else:
            return self.head, self.head_dist

    def reset_classifier(self, num_classes, global_pool=''):
        self.num_classes = num_classes
        self.head = nn.Linear(self.embed_dim, num_classes) if num_classes > 0 else nn.Identity()
        if self.num_tokens == 2:
            self.head_dist = nn.Linear(self.embed_dim, self.num_classes) if num_classes > 0 else nn.Identity()

    def forward_features(self, x, return_disentangled_features=False):
        """
        特征提取，支持解耦特征返回

        Args:
            x: 输入图像
            return_disentangled_features: 是否返回解耦的身份和变化特征

        Returns:
            如果return_disentangled_features=True且使用解耦适配器:
                (features, identity_features, variation_features, total_disentanglement_loss)
            否则: features
        """
        B = x.shape[0]
        x = self.patch_embed(x)

        cls_tokens = self.cls_token.expand(B, -1, -1)  # stole cls_tokens impl from Phil Wang, thanks
        x = torch.cat((cls_tokens, x), dim=1)
        x = x + self.pos_embed
        x = self.pos_drop(x)

        # 收集解耦特征和损失
        all_identity_features = []
        all_variation_features = []
        total_disentanglement_loss = 0.0
        use_disentangled = getattr(self.tuning_config, 'use_disentangled_adapter', False)

        for idx, blk in enumerate(self.blocks):
            if self.tuning_config.vpt_on:
                eee = self.embeddings[idx].expand(B, -1, -1)
                x = torch.cat([eee, x], dim=1)

            if return_disentangled_features and use_disentangled:
                # 获取解耦特征
                block_output = blk(x, return_disentangled_features=True)
                if len(block_output) == 4:  # 返回了解耦特征
                    x, identity_feat, variation_feat, disentangle_loss = block_output
                    all_identity_features.append(identity_feat)
                    all_variation_features.append(variation_feat)
                    if disentangle_loss is not None:
                        total_disentanglement_loss += disentangle_loss
                else:
                    x = block_output
            else:
                x = blk(x)

            if self.tuning_config.vpt_on:
                x = x[:, self.tuning_config.vpt_num:, :]

        if self.global_pool:
            x = x[:, 1:, :].mean(dim=1)  # global pool without cls token
            outcome = self.fc_norm(x)
        else:
            x = self.norm(x)
            outcome = x[:, 0]

        if return_disentangled_features and use_disentangled and all_identity_features:
            # 聚合所有层的解耦特征
            # 使用CLS token位置的特征
            aggregated_identity = torch.stack([feat[:, 0, :] for feat in all_identity_features], dim=1)  # [B, num_layers, identity_dim]
            aggregated_variation = torch.stack([feat[:, 0, :] for feat in all_variation_features], dim=1)  # [B, num_layers, variation_dim]

            # 平均池化或其他聚合方式
            final_identity = aggregated_identity.mean(dim=1)  # [B, identity_dim]
            final_variation = aggregated_variation.mean(dim=1)  # [B, variation_dim]

            return outcome, final_identity, final_variation, total_disentanglement_loss
        else:
            return outcome

    def forward(self, x, return_disentangled_features=False):
        """
        前向传播，支持解耦特征返回

        Args:
            x: 输入图像
            return_disentangled_features: 是否返回解耦特征

        Returns:
            如果return_disentangled_features=True:
                (logits, identity_features, variation_features, disentanglement_loss)
            否则: logits
        """
        if return_disentangled_features:
            features_output = self.forward_features(x, return_disentangled_features=True)
            if len(features_output) == 4:
                features, identity_features, variation_features, disentanglement_loss = features_output
            else:
                features = features_output
                identity_features = None
                variation_features = None
                disentanglement_loss = None
        else:
            features = self.forward_features(x)
            identity_features = None
            variation_features = None
            disentanglement_loss = None

        if self.head_dist is not None:
            logits, logits_dist = self.head(features), self.head_dist(features)
            if self.training and not torch.jit.is_scripting():
                # during inference, return the average of both classifier predictions
                final_logits = (logits, logits_dist)
            else:
                final_logits = (logits + logits_dist) / 2
        else:
            final_logits = self.head(features)

        if return_disentangled_features:
            return final_logits, identity_features, variation_features, disentanglement_loss
        else:
            return final_logits

    def extract_disentangled_features(self, x):
        """
        专门用于提取解耦特征的方法

        Args:
            x: 输入图像

        Returns:
            (combined_features, identity_features, variation_features, disentanglement_loss)
        """
        return self.forward_features(x, return_disentangled_features=True)


# def vit_base_patch16(**kwargs):
#     model = VisionTransformer(
#         patch_size=16, embed_dim=768, depth=12, num_heads=12, mlp_ratio=4, qkv_bias=True,
#         norm_layer=partial(nn.LayerNorm, eps=1e-6), **kwargs)
#     return model


# def vit_large_patch16(**kwargs):
#     model = VisionTransformer(
#         patch_size=16, embed_dim=1024, depth=24, num_heads=16, mlp_ratio=4, qkv_bias=True,
#         norm_layer=partial(nn.LayerNorm, eps=1e-6), **kwargs)
#     return model


# def vit_huge_patch14(**kwargs):
#     model = VisionTransformer(
#         patch_size=14, embed_dim=1280, depth=32, num_heads=16, mlp_ratio=4, qkv_bias=True,
#         norm_layer=partial(nn.LayerNorm, eps=1e-6), **kwargs)
#     return model


# def _create_vision_transformer(variant, pretrained=False, **kwargs):
#     if kwargs.get('features_only', None):
#         raise RuntimeError('features_only not implemented for Vision Transformer models.')

#     pretrained_cfg = resolve_pretrained_cfg(variant, pretrained_cfg=kwargs.pop('pretrained_cfg', None))
#     model = build_model_with_cfg(
#         VisionTransformer, variant, pretrained,
#         pretrained_cfg=pretrained_cfg,
#         pretrained_filter_fn=checkpoint_filter_fn,
#         pretrained_custom_load='npz' in pretrained_cfg['url'],
#         **kwargs)
#     return model




def vit_base_patch16_224_adapter(pretrained=False, **kwargs):
    
    model = VisionTransformer(patch_size=16, embed_dim=768, depth=12, num_heads=12, mlp_ratio=4, qkv_bias=True,
        norm_layer=partial(nn.LayerNorm, eps=1e-6), **kwargs)

    # checkpoint_model = torch.load('./pretrained_models/B_16-i21k-300ep-lr_0.001-aug_medium1-wd_0.1-do_0.0-sd_0.0.npz')
    checkpoint_model=timm.create_model("vit_base_patch16_224", pretrained=True, num_classes=0)
    state_dict = checkpoint_model.state_dict()
    # modify the checkpoint state dict to match the model
    # first, split qkv weight into q, k, v
    for key in list(state_dict.keys()):
        if 'qkv.weight' in key:
            qkv_weight = state_dict.pop(key)
            q_weight = qkv_weight[:768]
            k_weight = qkv_weight[768:768*2]
            v_weight = qkv_weight[768*2:]
            state_dict[key.replace('qkv.weight', 'q_proj.weight')] = q_weight
            state_dict[key.replace('qkv.weight', 'k_proj.weight')] = k_weight
            state_dict[key.replace('qkv.weight', 'v_proj.weight')] = v_weight
        elif 'qkv.bias' in key:
            qkv_bias = state_dict.pop(key)
            q_bias = qkv_bias[:768]
            k_bias = qkv_bias[768:768*2]
            v_bias = qkv_bias[768*2:]
            state_dict[key.replace('qkv.bias', 'q_proj.bias')] = q_bias
            state_dict[key.replace('qkv.bias', 'k_proj.bias')] = k_bias
            state_dict[key.replace('qkv.bias', 'v_proj.bias')] = v_bias
    # second, modify the mlp.fc.weight to match fc.weight
    for key in list(state_dict.keys()):
        if 'mlp.fc' in key:
            fc_weight = state_dict.pop(key)
            state_dict[key.replace('mlp.', '')] = fc_weight

    msg = model.load_state_dict(state_dict, strict=False)
    print(msg)

    # s=model.state_dict()
    # # print the keys in s
    # for key in s.keys():
    #     print(key)
    # # print the keys in checkpoint_model
    # for key in state_dict.keys():
    #     if key in s.keys():
    #         print(key, 'yes')
    #     else:
    #         print(key, 'NOOOOOOOOOOOOOOOOOOO')

    # freeze all but the adapter
    for name, p in model.named_parameters():
        if name in msg.missing_keys:
            p.requires_grad = True
        else:
            p.requires_grad = False 
    return model



def vit_base_patch16_224_in21k_adapter(pretrained=False, **kwargs):
    
    model = VisionTransformer(patch_size=16, embed_dim=768, depth=12, num_heads=12, mlp_ratio=4, qkv_bias=True,
        norm_layer=partial(nn.LayerNorm, eps=1e-6), **kwargs)

    # checkpoint_model = torch.load('./pretrained_models/B_16-i21k-300ep-lr_0.001-aug_medium1-wd_0.1-do_0.0-sd_0.0.npz')
    checkpoint_model=timm.create_model("vit_base_patch16_224_in21k", pretrained=True, num_classes=0)
    state_dict = checkpoint_model.state_dict()
    # modify the checkpoint state dict to match the model
    # first, split qkv weight into q, k, v
    for key in list(state_dict.keys()):
        if 'qkv.weight' in key:
            qkv_weight = state_dict.pop(key)
            q_weight = qkv_weight[:768]
            k_weight = qkv_weight[768:768*2]
            v_weight = qkv_weight[768*2:]
            state_dict[key.replace('qkv.weight', 'q_proj.weight')] = q_weight
            state_dict[key.replace('qkv.weight', 'k_proj.weight')] = k_weight
            state_dict[key.replace('qkv.weight', 'v_proj.weight')] = v_weight
        elif 'qkv.bias' in key:
            qkv_bias = state_dict.pop(key)
            q_bias = qkv_bias[:768]
            k_bias = qkv_bias[768:768*2]
            v_bias = qkv_bias[768*2:]
            state_dict[key.replace('qkv.bias', 'q_proj.bias')] = q_bias
            state_dict[key.replace('qkv.bias', 'k_proj.bias')] = k_bias
            state_dict[key.replace('qkv.bias', 'v_proj.bias')] = v_bias
    # second, modify the mlp.fc.weight to match fc.weight
    for key in list(state_dict.keys()):
        if 'mlp.fc' in key:
            fc_weight = state_dict.pop(key)
            state_dict[key.replace('mlp.', '')] = fc_weight

    msg = model.load_state_dict(state_dict, strict=False)
    print(msg)

    # s=model.state_dict()
    # # print the keys in s
    # for key in s.keys():
    #     print(key)
    # # print the keys in checkpoint_model
    # for key in state_dict.keys():
    #     if key in s.keys():
    #         print(key, 'yes')
    #     else:
    #         print(key, 'NOOOOOOOOOOOOOOOOOOO')

    # freeze all but the adapter
    for name, p in model.named_parameters():
        if name in msg.missing_keys:
            p.requires_grad = True
        else:
            p.requires_grad = False 
    return model

