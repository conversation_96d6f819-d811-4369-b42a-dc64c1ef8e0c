{"prefix": "debug_safe", "dataset": "cub", "memory_size": 0, "shuffle": true, "init_cls": 100, "increment": 10, "model_name": "ranpac", "backbone_type": "pretrained_vit_b16_224_adapter", "device": ["0"], "seed": [1993], "resume": false, "shot": 5, "use_simplecil": false, "tuned_epoch": 1, "init_lr": 0.01, "batch_size": 48, "weight_decay": 0.0005, "min_lr": 0, "ffn_num": 64, "optimizer": "sgd", "use_RP": true, "M": 10000, "fecam": false, "calibration": true, "knn_k": 5, "knn_distance_metric": "cosine", "knn_weight_decay": 0.1, "knn_adaptive_k": true, "knn_temperature": 16.0, "k_min": 3, "k_max": 21, "dynamic_k_method": "cosine_similarity", "cosine_temperature": 16.0, "_comment_debug": "=== 安全调试配置：极小的解耦损失权重 ===", "use_disentangled_adapter": true, "identity_bottleneck": 32, "variation_bottleneck": 32, "disentangle_loss_weight": 0.0001, "variation_cov_weight": 0.1, "decorrelation_weight": 0.1, "orthogonal_weight": 0.01, "mutual_info_weight": 0.01, "contrastive_weight": 0.01, "disentangle_temperature": 0.1, "orthogonal_constraint": false, "_comment_note": "使用极小的损失权重和禁用正交约束来避免数值不稳定"}